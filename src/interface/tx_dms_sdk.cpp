#include "tx_dms_sdk.h"
#include <mutex>
#include "CalmCarLog.h"
#include "activate.h"
#include "calmcar_dms_process.h"

#if defined(BYD_SC3E)
#define SDK_VERSION ("2.01.07.2025.02.20.3")
#endif

#if defined(BYD_EQ)
#define SDK_VERSION ("2.01.07.2025.02.06.1")
#endif

#if defined(BYD_EQ_R)
#define SDK_VERSION ("2.01.07.2025.02.18.1")
#endif

#if defined(BYD_HA6)
#define SDK_VERSION ("2.01.07.2025.02.13.3")
#endif

#if defined(BYD_SA2)
#define SDK_VERSION ("2.01.07.2025.02.11.2")
#endif

#if defined(BYD_SC2EDE)
#define SDK_VERSION ("2.01.07.2025.01.18.1")
#endif

#if defined(BYD_HKH_L)
#define SDK_VERSION ("2.01.07.2025.02.06.1")
#endif

#if defined(BYD_HKH_R)
#define SDK_VERSION ("2.01.07.2025.02.07.1")
#endif

#if defined(BYD_SC3EFE)
#define SDK_VERSION ("2.01.07.2025.07.17.2")
#endif

#if defined(BYD_SC3EFE_R)
#define SDK_VERSION ("2.01.07.2025.07.02.1")
#endif

#define EXPORT __attribute__((visibility("default")))

using namespace tongxing;

std::mutex txzl_dms_mutex;
#define TX_DMS_UNIQUE_LOCK std::unique_lock<std::mutex> m(txzl_dms_mutex);
long g_hDms = 0;
extern "C" {
//////////////////////////////////////////////////////////////////////////////////////////
//	int TXrDmsActivate(char*,int)
//	功能:
//		根据激活码校验算法是否可以被激活
//	返回值:
//		Dms激活状态	返回0时表示激活成功
//	输入:
//      activate_code           激活码
//      activate_code_len       激活码长度
//////////////////////////////////////////////////////////////////////////////////////////
EXPORT int TXDmsActivate(char* activate_code, int activate_code_len) {
#ifdef __USE_ACTIVATE__
    // ActivateService::instance().setuuid(std::string(imei, imei_len));
    int status = ActivateService::instance().run(std::string(activate_code), activate_code_len);
    TX_LOG_DEBUG("DMS INTERFACE",
                 "TXDmsActivate activate_code=%s , activate_code_len=%d , status=%d",
                 std::string(activate_code).c_str(), activate_code_len, status);
    return status;
#else
    return 0;
#endif
}

//////////////////////////////////////////////////////////////////////////////////////////
//	long TXDmsCreate(const char *)
//	功能:
//		Dms创建并初始化
//	返回值:
//		Dms句柄	返回0时表示创建失败
//	输入:
//       config_file    配置文件路径，当设置成NULL时，使用内置的默认配置
//////////////////////////////////////////////////////////////////////////////////////////
EXPORT long TXDmsCreate(const char* config_file, const char* cache_path) {
    TX_DMS_UNIQUE_LOCK
    if (g_hDms == 0) {
        tongxing::CcDmsProcess* ptr = new tongxing::CcDmsProcess();
        if (ptr == NULL) {
            return 0;
        }
        int res = ptr->Init(config_file, cache_path);
        if (res != 0) {
            delete ptr;
        }
        g_hDms = (long)ptr;
        TX_LOG_DEBUG("DMS INTERFACE", "TXDmsCreate");
        return (long)ptr;
    } else {
        TX_LOG_DEBUG("DMS INTERFACE", "TXDmsCreate repeated call ");
        return g_hDms;
    }
}

//////////////////////////////////////////////////////////////////////////////////////////
//	const char* TXDmsGetVersion()
//	功能:
//		Dms获取算法版本号
//	返回值:
//		返回 0 获取成功
//	输入:
//       hDms			Dms句柄
//       version         dms版本号信息
//////////////////////////////////////////////////////////////////////////////////////////
EXPORT const char* TXDmsGetVersion() {
#if defined(BYD_SC3E)
    return "2.01.03.2024.06.29.2";
#endif

#if defined(BYD_EQ)
    return "2.01.07.2024.09.22.1";
#endif

#if defined(BYD_EQ_R)
    return SDK_VERSION;
#endif

#if defined(BYD_HA6)
    return "2.01.07.2024.12.16.1";
#endif

#if defined(BYD_SA2)
    return SDK_VERSION;
#endif

#if defined(BYD_HKH_L)
    return SDK_VERSION;
#endif

#if defined(BYD_SC2EDE)
    return SDK_VERSION;
#endif

#if defined(BYD_HKH_R)
    return SDK_VERSION;
#endif

#if defined(BYD_SC3EFE)
    return "2.01.03.2024.06.29.2";
#endif
#if defined(BYD_SC3EFE_R)
    return SDK_VERSION;
#endif
}

EXPORT const char* TXDmsGetRealVersion() {
    return SDK_VERSION;
}

//////////////////////////////////////////
//	int TXDmsDestroy(long)
//	功能:
//		Dms析构。
//	返回值:
//		返回0表示执行成功，非0表示失败
//	输入:
//		hDms			Dms句柄
//////////////////////////////////////////
EXPORT int TXDmsDestroy(long hDms) {
    TX_DMS_UNIQUE_LOCK
    TX_LOG_DEBUG("DMS INTERFACE", "TXDmsDestroy hDms=0x%x", (void*)hDms);
    tongxing::CcDmsProcess* ptr = (tongxing::CcDmsProcess*)hDms;
    if (ptr == NULL || g_hDms != hDms) {
        return -1;
    }
    delete ptr;
    g_hDms = 0;
    return 0;
}

//////////////////////////////////////////////////////////////////////
//	int TXDmsSetInput(long hDms, const TXImageInfo *image,const TXCarInfo *carInfo, long long frame_id);
//	功能:
//		往算法传入图像和车速
//	返回值:
//      返回0表示执行成功，非0表示失败
//	输入:
//		hDms			Dms句柄
//		image 		    图像信息
//      carInfo         车辆状态信息
//      frame_id        输入图像的ID，一般每传进去一帧图片frame_id就加1
//////////////////////////////////////////////////////////////////////
EXPORT int TXDmsSetInput(long hDms, const TXImageInfo* image, TXDmsResult* result) {
    TX_DMS_UNIQUE_LOCK
    TX_LOG_DEBUG("DMS INTERFACE", "TXDmsSetInput start hDms=0x%x", (void*)hDms);
    tongxing::CcDmsProcess* ptr = (tongxing::CcDmsProcess*)hDms;
    if (ptr == NULL || g_hDms != hDms) {
        return -1;
    }
    auto ret = ptr->SetInput(image, result);
    TX_LOG_DEBUG("DMS INTERFACE", "TXDmsSetInput end");
    return ret;
}

EXPORT int TXDmsRestAlarm(long hDms) {
    tongxing::CcDmsProcess* ptr = (tongxing::CcDmsProcess*)hDms;
    if (ptr == NULL || g_hDms != hDms) {
        return -1;
    }
    int ret = ptr->RestAlarm();
    return 0;
}

EXPORT int TXDmsDistractRestAlarm(long hDms) {
    tongxing::CcDmsProcess* ptr = (tongxing::CcDmsProcess*)hDms;
    if (ptr == NULL || g_hDms != hDms) {
        return -1;
    }
    int ret = ptr->RestDistractAlarm();
    return 0;
}

//////////////////////////////////////////////////////////////////////
//	int TXDmsSetDriverRoi(long, TXPoint2i*, TXPoint2i*)
//	功能:
//		设置司机检测区域，用于区分司机和乘客
//	返回值:
//      返回0表示执行成功，非0表示失败
//	输入:
//		hDms			Dms句柄
//		left_top_point 		司机检测区域在的左上点坐标
//      right_bottom_point       司机检测区域在的右下点坐标
//////////////////////////////////////////////////////////////////////
EXPORT int TXDmsSetDriverRoi(long hDms, TXPoint2i* left_top_point, TXPoint2i* right_bottom_point) {
    TX_DMS_UNIQUE_LOCK
    TX_LOG_DEBUG("DMS INTERFACE", "TXDmsSetDriverRoi hDms=0x%x", (void*)hDms);
    tongxing::CcDmsProcess* ptr = (tongxing::CcDmsProcess*)hDms;
    ptr->SetDriverRoi(left_top_point, right_bottom_point);
    return 0;
}

//////////////////////////////////////////////////////////////////////
//	int TXDmsUpdataCarInfo(long，const TXCarInfo *)
//	功能:
//		更新车辆信息
//	返回值:
//      返回0表示执行成功，非0表示失败
//	输入:
//		hDms			Dms句柄
//		carInfo 		车型信息指针
//////////////////////////////////////////////////////////////////////
EXPORT int TXDmsUpdataCarInfo(long hDms, const TXCarInfo* carInfo) {
    TX_DMS_UNIQUE_LOCK
    TX_LOG_DEBUG("DMS INTERFACE", "TXDmsUpdataCarInfo hDms=0x%x", (void*)hDms);
    tongxing::CcDmsProcess* ptr = (tongxing::CcDmsProcess*)hDms;
    ptr->updateCarInfo(carInfo);
    return 0;
}

#ifdef WITH_PHONE_SMOKING_DET
//////////////////////////////////////////////////////////////////////
//	int TXDmsEnablePhoneAndSmokingDetect(long hDms, unsigned char enable_flag);
//	功能:
//		开启和关闭抽烟打电话检测功能
//	返回值:
//      返回0表示执行成功，非0表示失败
//	输入:
//		hDms			Dms句柄
//		enable_flag     开启标志，1为开启，0为关闭
//////////////////////////////////////////////////////////////////////
EXPORT int TXDmsEnablePhoneAndSmokingDetect(long hDms, unsigned char enable_flag) {
    TX_DMS_UNIQUE_LOCK
    TX_LOG_DEBUG("DMS INTERFACE", "TXDmsEnablePhoneAndSmokingDetect hDms=0x%x flag=%d", (void*)hDms,
                 enable_flag);
    tongxing::CcDmsProcess* ptr = (tongxing::CcDmsProcess*)hDms;
    ptr->SetPhoneAndSmokingDetectEnableStatus(enable_flag);
    return 0;
}
#endif

//////////////////////////////////////////////////////////////////////////////////////////
//	int TXDmsSetLogLevel(TXLogLevelType log_level)
//	功能:
//		设置日志等级
//	返回值:
//		返回0表示执行成功，非0表示失败
//	输入:
//		log_level			日志级别
//////////////////////////////////////////////////////////////////////////////////////////
EXPORT int TXDmsSetLogLevel(TXLogLevelType log_level) {
    CalmCarLog::getInstance()->set_calmcar_log_level((LogLevel)log_level);
    return 0;
}
}
